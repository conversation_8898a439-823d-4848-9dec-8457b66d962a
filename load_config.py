#!/usr/bin/env python
# encoding:utf-8

"""
配置加载器
从config.env文件加载环境变量配置
"""

import os
import logging

def load_env_config(config_file='config.env'):
    """从配置文件加载环境变量"""
    if not os.path.exists(config_file):
        print(f"警告: 配置文件 {config_file} 不存在，使用默认配置")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过注释和空行
                if line.startswith('#') or not line:
                    continue
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 设置环境变量
                    os.environ[key] = value
                    print(f"加载配置: {key}={value}")
        
        print(f"配置文件 {config_file} 加载完成")
        
    except Exception as e:
        print(f"加载配置文件失败: {e}")

if __name__ == "__main__":
    # 测试配置加载
    load_env_config()
