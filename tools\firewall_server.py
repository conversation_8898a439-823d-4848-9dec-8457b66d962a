#!/usr/bin/env python
# encoding:utf-8

"""
防火墙IP封禁器 MCP Server
基于 Model Context Protocol (MCP) 的防火墙IP封禁工具集成
提供2个主要功能：
1. 封禁IP地址
2. 查询封禁状态
"""

from mcp.server.fastmcp import FastMCP
import logging
import requests
import re
import time
from typing import Dict, Optional, Any, List
import urllib3
import os
import sys

# 导入缓存系统
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from load_config import load_env_config

# 加载配置
load_env_config()

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("防火墙IP封禁服务")


class FirewallConfig:
    """防火墙配置类"""
    
    def __init__(self):
        self.target_ip = "*************"
        self.username = "wabsuper"
        self.password = "whgD@955989"
        self.host_name_encoded = "01-%CD%F8%B0%B2%B7%E2%BD%FB%C4%DA%CD%F8%B5%D8%D6%B7"
        self.timeout = 10
        self.max_retries = 3


class FirewallBlocker:
    """防火墙封禁管理类"""
    
    def __init__(self, config: FirewallConfig):
        self.config = config
        self.base_url = f"https://{config.target_ip}/cgi/maincgi.cgi"
        self.session = None
        self.is_logged_in = False
        
    def _create_session(self):
        """创建HTTP会话"""
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        })
        
    def _login(self) -> bool:
        """登录防火墙系统"""
        if not self.session:
            self._create_session()
            
        logger.info("正在尝试登录防火墙系统...")
        
        login_payload = {
            "username": self.config.username,
            "passwd": self.config.password,
            "loginSubmitIpt": ""
        }
        
        try:
            login_resp = self.session.post(
                f"{self.base_url}?Url=Index",
                data=login_payload,
                verify=False,
                timeout=self.config.timeout
            )
            login_resp.raise_for_status()
            
            if "username" in login_resp.text and "passwd" in login_resp.text:
                logger.error("登录失败！请检查用户名和密码")
                return False
            
            logger.info("✅ 登录成功！")
            self.is_logged_in = True
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"登录请求失败: {e}")
            return False
    
    def _get_blocked_ips(self) -> Optional[List[str]]:
        """获取当前已封禁的IP列表"""
        if not self.is_logged_in:
            if not self._login():
                return None
                
        logger.info("正在查询已封禁的IP列表...")
        edit_page_url = f"{self.base_url}?Url=HostObj&Act=Edit&Name={self.config.host_name_encoded}"
        
        try:
            get_ip_resp = self.session.get(edit_page_url, verify=False, timeout=self.config.timeout)
            get_ip_resp.raise_for_status()
            
            match = re.search(r"var ip_str = '(.*?)';", get_ip_resp.text, re.S)
            
            if not match:
                logger.error("无法在页面上找到已封禁的IP列表")
                return None
            
            ip_string = match.group(1).strip()
            old_ips = ip_string.split() if ip_string else []
            
            logger.info(f"✅ 查询成功！当前已封禁 {len(old_ips)} 个IP")
            return old_ips
            
        except requests.exceptions.RequestException as e:
            logger.error(f"查询IP列表失败: {e}")
            return None
    
    def _add_ip_to_list(self, ip_to_add: str, current_ips: List[str]) -> bool:
        """将IP添加到封禁列表"""
        logger.info(f"正在添加新IP '{ip_to_add}' 到封禁列表...")
        
        edit_page_url = f"{self.base_url}?Url=HostObj&Act=Edit&Name={self.config.host_name_encoded}"
        
        # 构建POST请求体
        payload_parts = [
            f"def_host_name={self.config.host_name_encoded}",
            "def_host_mac=00%3A00%3A00%3A00%3A00%3A00"
        ]
        
        all_ips = current_ips + [ip_to_add]
        for ip in all_ips:
            payload_parts.append(f"def_host_ipad={ip}")
            
        payload_parts.extend([
            f"host_ipad_input={ip_to_add}",
            f"name_hidden={self.config.host_name_encoded}",
            "def_host_frompage=",
            "def_host_from=",
            "def_host_edt_but=+%C8%B7%B6%A8+"
        ])
        
        final_payload_string = "&".join(payload_parts)
        
        try:
            update_headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Origin": f"https://{self.config.target_ip}",
                "Referer": edit_page_url
            }
            
            update_resp = self.session.post(
                edit_page_url, 
                data=final_payload_string.encode('utf-8'),
                headers=update_headers,
                verify=False, 
                timeout=self.config.timeout
            )
            update_resp.raise_for_status()
            
            if update_resp.status_code == 200:
                logger.info(f"✅ 提交成功！IP '{ip_to_add}' 已被添加")
                return True
            else:
                logger.error(f"提交失败，服务器返回状态码: {update_resp.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"提交更新请求失败: {e}")
            return False
    
    def _validate_ip(self, ip_address: str) -> bool:
        """验证IP地址格式"""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip_address):
            return False
        
        parts = ip_address.split('.')
        for part in parts:
            if int(part) > 255:
                return False
        
        return True
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
            self.session = None
            self.is_logged_in = False


# 全局变量用于缓存登录状态
_firewall_blocker = None
_last_login_time = None
LOGIN_CACHE_DURATION = 3600  # 登录缓存1小时


def get_authenticated_blocker():
    """
    获取已认证的防火墙封禁器实例，如果需要则重新登录
    返回: (firewall_blocker, success)
    """
    global _firewall_blocker, _last_login_time

    current_time = time.time()

    # 检查是否需要重新登录
    if (_firewall_blocker is None or
        _last_login_time is None or
        current_time - _last_login_time > LOGIN_CACHE_DURATION):

        logger.info("需要重新登录防火墙系统")

        # 创建防火墙封禁器
        config = FirewallConfig()
        blocker = FirewallBlocker(config)
        
        # 尝试登录
        if not blocker._login():
            logger.error("登录失败，无法继续后续操作")
            return None, False

        # 更新全局缓存
        _firewall_blocker = blocker
        _last_login_time = current_time

        logger.info("登录成功，防火墙封禁器已缓存")

    return _firewall_blocker, True


@mcp.tool()
def block_ip_address(ip_address: str, reason: str = "Manual block") -> Dict[str, Any]:
    """封禁指定的IP地址到防火墙封禁列表。

    Args:
        ip_address: 要封禁的IP地址，格式为IPv4地址（如：***********）

    Returns:
        Dict[str, Any]: 包含封禁操作结果的字典
            - status: "success" 或 "error"
            - data: 封禁操作详细信息（成功时）
            - message: 错误信息（失败时）
            - ip_address: 操作的IP地址
            - already_blocked: 是否已被封禁
            - total_blocked_ips: 总封禁IP数量
    """
    logger.info(f"[MCP工具] 开始封禁IP地址: {ip_address}")

    try:
        # 获取已认证的防火墙封禁器
        blocker, success = get_authenticated_blocker()

        if not success:
            return {
                "status": "error",
                "message": "登录防火墙系统失败",
                "ip_address": ip_address
            }

        # 验证IP格式
        if not blocker._validate_ip(ip_address):
            return {
                "status": "error",
                "message": "IP地址格式不正确",
                "ip_address": ip_address
            }

        # 获取当前封禁列表
        current_ips = blocker._get_blocked_ips()
        if current_ips is None:
            return {
                "status": "error",
                "message": "无法获取当前封禁列表",
                "ip_address": ip_address
            }

        # 检查IP是否已被封禁
        if ip_address in current_ips:
            logger.info(f"IP地址 '{ip_address}' 已存在于封禁列表中")
            return {
                "status": "success",
                "data": {
                    "ip_address": ip_address,
                    "action": "block",
                    "success": True,
                    "message": "IP地址已存在于封禁列表中，无需重复添加",
                    "already_blocked": True,
                    "total_blocked_ips": len(current_ips)
                },
                "message": f"IP地址 {ip_address} 已存在于封禁列表中"
            }

        # 添加IP到封禁列表
        add_success = blocker._add_ip_to_list(ip_address, current_ips)

        if add_success:
            logger.info(f"成功封禁IP地址: {ip_address}")
            return {
                "status": "success",
                "data": {
                    "ip_address": ip_address,
                    "action": "block",
                    "success": True,
                    "message": "IP地址已成功添加到封禁列表",
                    "already_blocked": False,
                    "total_blocked_ips": len(current_ips) + 1
                },
                "message": f"IP地址 {ip_address} 已成功添加到封禁列表"
            }
        else:
            return {
                "status": "error",
                "message": "添加IP到封禁列表失败",
                "ip_address": ip_address
            }

    except Exception as e:
        logger.error(f"封禁IP地址出错: {str(e)}")
        return {
            "status": "error",
            "message": f"封禁IP地址出错: {str(e)}",
            "ip_address": ip_address
        }


@mcp.tool()
def query_block_status(ip_address: Optional[str] = None) -> Dict[str, Any]:
    """查询IP地址的封禁状态或获取所有已封禁的IP列表。

    Args:
        ip_address: 要查询状态的IP地址，留空则查询所有已封禁IP

    Returns:
        Dict[str, Any]: 包含查询结果的字典
            - status: "success" 或 "error"
            - data: 查询结果详细信息（成功时）
            - message: 错误信息（失败时）
            - query_ip: 查询的IP地址或"all"
            - is_blocked: 是否被封禁（查询单个IP时）
            - blocked_ips: 所有已封禁IP列表（查询全部时）
            - total_blocked_ips: 总封禁IP数量
    """
    logger.info(f"[MCP工具] 查询封禁状态: {ip_address if ip_address else '所有IP'}")

    try:
        # 获取已认证的防火墙封禁器
        blocker, success = get_authenticated_blocker()

        if not success:
            return {
                "status": "error",
                "message": "登录防火墙系统失败"
            }

        # 获取当前封禁列表
        blocked_ips = blocker._get_blocked_ips()

        if blocked_ips is None:
            return {
                "status": "error",
                "message": "无法获取封禁列表"
            }

        if ip_address:
            # 查询特定IP
            if not blocker._validate_ip(ip_address):
                return {
                    "status": "error",
                    "message": "IP地址格式不正确",
                    "query_ip": ip_address
                }

            is_blocked = ip_address in blocked_ips
            logger.info(f"IP地址 {ip_address} 封禁状态: {'已封禁' if is_blocked else '未封禁'}")

            return {
                "status": "success",
                "data": {
                    "query_ip": ip_address,
                    "is_blocked": is_blocked,
                    "total_blocked_ips": len(blocked_ips),
                    "message": "IP地址在封禁列表中" if is_blocked else "IP地址不在封禁列表中"
                },
                "message": f"IP地址 {ip_address} {'在' if is_blocked else '不在'}封禁列表中"
            }
        else:
            # 查询所有封禁IP
            logger.info(f"成功获取所有封禁IP，共 {len(blocked_ips)} 个")

            return {
                "status": "success",
                "data": {
                    "query_ip": "all",
                    "total_blocked_ips": len(blocked_ips),
                    "blocked_ips": blocked_ips,
                    "message": f"当前共有 {len(blocked_ips)} 个IP被封禁"
                },
                "message": f"成功获取所有封禁IP，共 {len(blocked_ips)} 个"
            }

    except Exception as e:
        logger.error(f"查询封禁状态出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询封禁状态出错: {str(e)}"
        }


if __name__ == "__main__":
    logger.info("启动防火墙IP封禁服务MCP服务器")
    mcp.run(transport="stdio")
