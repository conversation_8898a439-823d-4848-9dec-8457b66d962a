from mcp.server.fastmcp import FastMCP
import logging
import poplib
import email
import json
import os
from email.message import Message
from typing import List, Dict, Any, Tuple
from datetime import datetime
import time
import base64
from email.header import decode_header
from email.utils import parsedate_to_datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 导入缓存系统
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from load_config import load_env_config

# 加载配置
load_env_config()

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


MAX_ATTACHMENT_SIZE = 5 * 1024 * 1024
READ_EMAILS_FILE = "read_emails.json"
MAX_WORKERS = 2  # 同时处理的最大账户数

# 创建 FastMCP 实例
mcp = FastMCP("邮件服务")

class EmailStatusTracker:
    """邮件状态跟踪器（支持多账户）"""
    def __init__(self, storage_path: str = READ_EMAILS_FILE):
        self.storage_path = Path(storage_path)
        self.read_data = {}  # {account: set(email_ids)}
        self._load()
    
    def _load(self):
        """加载已读状态"""
        try:
            if self.storage_path.exists():
                with open(self.storage_path, 'r') as f:
                    self.read_data = json.load(f)
                    # 转换为集合格式
                    for account in self.read_data:
                        self.read_data[account] = set(self.read_data[account])
        except Exception as e:
            logger.warning(f"加载已读状态失败: {str(e)}")
            self.read_data = {}
    
    def _save(self):
        """保存已读状态"""
        try:
            # 转换为列表格式以便JSON序列化
            save_data = {account: list(email_ids) 
                        for account, email_ids in self.read_data.items()}
            with open(self.storage_path, 'w') as f:
                json.dump(save_data, f, indent=2)
        except Exception as e:
            logger.warning(f"保存已读状态失败: {str(e)}")
    
    def is_read(self, account: str, email_id: str) -> bool:
        """检查邮件是否已读"""
        return email_id in self.read_data.get(account, set())
    
    def mark_as_read(self, account: str, email_ids: List[str]):
        """标记邮件为已读"""
        if account not in self.read_data:
            self.read_data[account] = set()
        
        need_save = False
        for email_id in email_ids:
            if email_id not in self.read_data[account]:
                self.read_data[account].add(email_id)
                need_save = True
        
        if need_save:
            self._save()

class EmailClient:
    def __init__(self, server: str, username: str, password: str, 
                 port: int = 110, tracker: EmailStatusTracker = None):
        self.pop_server = server
        self.username = username
        self.password = password
        self.port = port
        self.connection = None
        self.tracker = tracker or EmailStatusTracker()
    
    def connect(self) -> bool:
        """连接服务器"""
        try:
            self.connection = poplib.POP3(self.pop_server, self.port, timeout=10)
            self.connection.user(self.username)
            self.connection.pass_(self.password)
            logger.info(f"{self.username} 连接成功")
            return True
        except Exception as e:
            logger.error(f"{self.username} 连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.connection:
            try:
                self.connection.quit()
            except:
                pass
            finally:
                self.connection = None
    
    def _generate_email_id(self, msg: Message) -> str:
        """生成唯一邮件标识符"""
        message_id = msg.get('Message-ID')
        if message_id:
            return message_id
        
        date = msg.get('Date', '')
        from_ = msg.get('From', '')
        subject = msg.get('Subject', '')
        return f"{hash(date)}-{hash(from_)}-{hash(subject)}"
    
    def _extract_body(self, msg: Message) -> Dict[str, str]:
        """提取邮件正文"""
        body = {"text": "", "html": ""}
        if msg.is_multipart():
            for part in msg.walk():
                content_disposition = str(part.get("Content-Disposition", ""))
                if "attachment" in content_disposition:
                    continue
                content_type = part.get_content_type()
                payload = self._decode_part(part)
                if content_type == "text/plain":
                    body["text"] += payload
                elif content_type == "text/html":
                    body["html"] += payload
        else:
            body["text"] = self._decode_part(msg)
        return body
    
    def _extract_attachments(self, msg: Message) -> List[Dict[str, Any]]:
        """提取附件信息"""
        attachments = []
        for part in msg.walk():
            filename = part.get_filename()
            if filename and part.get_content_disposition() == 'attachment':
                try:
                    payload = part.get_payload(decode=True) or b""
                    attachment = {
                        "filename": self._decode_header(filename),
                        "content_type": part.get_content_type(),
                        "size": len(payload),
                    }
                    if len(payload) <= MAX_ATTACHMENT_SIZE:
                        attachment["data"] = base64.b64encode(payload).decode('utf-8')
                    else:
                        attachment["data"] = None
                        attachment["message"] = "附件过大，已跳过"
                    attachments.append(attachment)
                except Exception as e:
                    logger.warning(f"附件处理失败: {str(e)}")
        return attachments
    
    def _decode_part(self, part) -> str:
        """解码邮件部分"""
        try:
            payload = part.get_payload(decode=True)
            if not payload:
                return ""
            charset = part.get_content_charset() or 'utf-8'
            return payload.decode(charset, errors='ignore')
        except Exception as e:
            logger.warning(f"内容解码失败: {str(e)}")
            return "[内容解码失败]"
    
    def _decode_header(self, header):
        """解码邮件头"""
        if not header:
            return ""
        try:
            decoded = decode_header(header)
            return ''.join([t[0].decode(t[1] or 'utf-8', errors='ignore') 
                        if isinstance(t[0], bytes) else t[0] for t in decoded])
        except Exception as e:
            logger.warning(f"邮件头解码失败: {str(e)}")
            return header
    
    def fetch_unread_emails(self) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取当前账户的未读邮件及数量
        
        返回:
            Tuple[List[Dict], int]: (未读邮件列表, 未读邮件总数)
        """
        try:
            if not self.connect():
                return [], 0

            email_count, _ = self.connection.stat()
            if email_count == 0:
                return [], 0

            unread_emails = []
            
            for i in range(1, email_count + 1):
                try:
                    response = self.connection.retr(i)
                    msg = email.message_from_bytes(b'\r\n'.join(response[1]))
                    
                    email_id = self._generate_email_id(msg)
                    if self.tracker.is_read(self.username, email_id):
                        continue
                    
                    date_str = msg.get('Date')
                    email_date = parsedate_to_datetime(date_str) if date_str else datetime.now()
                    
                    email_data = {
                        "account": self.username,
                        "id": email_id,
                        "server_id": i,
                        "subject": self._decode_header(msg.get('Subject', '无主题')),
                        "from": self._decode_header(msg.get('From', '未知发件人')),
                        "date": email_date.strftime("%Y-%m-%d %H:%M:%S"),
                        "body": self._extract_body(msg),
                        "attachments": self._extract_attachments(msg)
                    }
                    unread_emails.append(email_data)
                
                except Exception as e:
                    logger.warning(f"{self.username} 邮件 {i} 处理失败: {str(e)}")
                    continue

            return unread_emails, len(unread_emails)

        except Exception as e:
            logger.error(f"{self.username} 获取未读邮件失败: {str(e)}")
            return [], 0
        finally:
            self.disconnect()


@mcp.tool()
def check_new_mail() -> Tuple[List[Dict[str, Any]], int]:
    """收取到新邮件时，发起通知。此工具用于检查邮箱并返回新收到的未读邮件列表。
    
    """
    logger.info("调用check_new_mail方法，检查新邮件")
    
    accounts = [
    {
        "server": "pop3.hb.sgcc.com.cn",
        "username": "<EMAIL>",
        "password": "whgd!@95598",
        "port": 110
    },
    {
        "server": "pop3.hb.sgcc.com.cn",
        "username": "<EMAIL>",
        "password": "2017xt@wh",
        "port": 110
    }
    ]

    tracker = EmailStatusTracker()
    all_unread = []
    total_unread = 0
    
    def process_account(account):
        nonlocal total_unread
        client = EmailClient(
            server=account['server'],
            username=account['username'],
            password=account['password'],
            port=account.get('port', 110),
            tracker=tracker
        )
        emails, count = client.fetch_unread_emails()
        return emails, count
    
    # 使用线程池并行获取
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        results = list(executor.map(process_account, accounts))
    
    # 合并结果
    for emails, count in results:
        all_unread.extend(emails)
        total_unread += count
    
    # 标记为已读
    if all_unread:
        # Re-initialize tracker to ensure it's fresh before marking
        tracker = EmailStatusTracker()
        for email_item in all_unread:
            tracker.mark_as_read(email_item['account'], [email_item['id']])

    return all_unread, total_unread

if __name__ == "__main__":
    # logger.info("========== 开始测试邮件读取功能 ==========")
    
    # # 调用核心函数进行测试
    # try:
    #     all_unread_emails, total_count = check_new_mail()
        
    #     logger.info(f"测试完成，共发现 {total_count} 封新的未读邮件。")
        
    #     if all_unread_emails:
    #         logger.info("邮件详情如下：")
    #         for i, email_data in enumerate(all_unread_emails, 1):
    #             print("-" * 50)
    #             print(f"邮件 #{i}")
    #             print(f"  > 账户: {email_data.get('account')}")
    #             print(f"  > 发件人: {email_data.get('from')}")
    #             print(f"  > 主题: {email_data.get('subject')}")
    #             print(f"  > 日期: {email_data.get('date')}")
                
    #             text_body = email_data.get('body', {}).get('text', '无文本正文').strip()
    #             preview = (text_body[:120] + '...') if len(text_body) > 120 else text_body
    #             print(f"  > 正文预览: {preview}")
                
    #             attachments = email_data.get('attachments', [])
    #             if attachments:
    #                 print(f"  > 附件 ({len(attachments)}个):")
    #                 for att in attachments:
    #                     att_info = f"{att.get('filename')} ({att.get('size')} bytes)"
    #                     if att.get('message'):
    #                         att_info += f" - {att.get('message')}"
    #                     print(f"    - {att_info}")
    #             else:
    #                 print("  > 附件: 无")
    #         print("-" * 50)
    #     else:
    #         logger.info("所有账户均无新的未读邮件。")
    # except Exception as e:
    #     logger.error(f"邮件读取测试过程中发生错误: {e}", exc_info=True)

    # logger.info("========== 邮件读取功能测试结束 ==========")
    
    logger.info("启动邮件服务MCP服务器")
    mcp.run(transport="stdio")