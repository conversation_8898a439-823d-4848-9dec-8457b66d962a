from mcp.server.fastmcp import FastMCP
import logging
from typing import List, Dict, Literal
import os
import sys

# 导入缓存系统
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from load_config import load_env_config

# 加载配置
load_env_config()

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建 FastMCP 实例
mcp = FastMCP("S60000业务流服务")

@mcp.tool()
def get_pending_items(module_type: Literal["工作通知", "工作任务", "工作联系单", "预警单反馈", "全省预警通告"]) -> List[Dict[str, str]]:
    """从S60000系统的指定工作模块中获取待处理的条目列表。
    
    Args:
        module_type: 工作模块类型，可选值：工作通知、工作任务、工作联系单、预警单反馈、全省预警通告
        
    Returns:
        待处理条目列表，每个元素包含id、title、sender、received_date字段
    """
    logger.info("调用get_pending_items方法: module_type=%s", module_type)
    
    # 根据不同模块类型返回固定的测试数据
    if module_type == "工作通知":
        return [
            {
                "id": "WN001",
                "title": "关于加强网络安全防护的通知",
                "sender": "网络安全部",
                "received_date": "2024-01-15 09:30:00"
            },
            {
                "id": "WN002", 
                "title": "系统维护计划通知",
                "sender": "运维中心",
                "received_date": "2024-01-15 14:20:00"
            }
        ]
    elif module_type == "工作任务":
        return [
            {
                "id": "WT001",
                "title": "漏洞扫描任务执行",
                "sender": "安全管理员",
                "received_date": "2024-01-14 16:45:00"
            },
            {
                "id": "WT002",
                "title": "安全策略更新任务",
                "sender": "策略管理组",
                "received_date": "2024-01-15 08:15:00"
            }
        ]
    elif module_type == "工作联系单":
        return [
            {
                "id": "WC001",
                "title": "跨部门协调联系单",
                "sender": "协调办公室",
                "received_date": "2024-01-15 11:00:00"
            }
        ]
    elif module_type == "预警单反馈":
        return [
            {
                "id": "WF001",
                "title": "高危漏洞预警反馈",
                "sender": "应急响应组",
                "received_date": "2024-01-15 13:30:00"
            },
            {
                "id": "WF002",
                "title": "异常流量预警处理反馈",
                "sender": "监控中心",
                "received_date": "2024-01-15 15:45:00"
            }
        ]
    elif module_type == "全省预警通告":
        return [
            {
                "id": "PA001",
                "title": "全省网络安全态势预警",
                "sender": "省网络安全中心",
                "received_date": "2024-01-15 10:00:00"
            }
        ]
    else:
        return []

if __name__ == "__main__":
    logger.info("启动S60000业务流服务MCP服务器")
    mcp.run(transport="stdio")
