#!/usr/bin/env python
# encoding:utf-8

"""
直接调用MCP工具函数的脚本
复制所有@mcp.tool()函数的实现，去掉装饰器，可以直接调用获取结果
"""

import os
import sys
import json
import logging
import hashlib
from datetime import datetime

# 添加tools目录到路径
sys.path.append('./tools')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def ensure_cache_dir():
    """确保缓存目录存在"""
    cache_dir = './cache_data'
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
        logger.info(f"创建缓存目录: {cache_dir}")
    return cache_dir

def generate_cache_key(function_name: str, *args, **kwargs) -> str:
    """生成缓存键"""
    params_str = f"{args}_{kwargs}"
    hash_obj = hashlib.md5(params_str.encode('utf-8'))
    hash_hex = hash_obj.hexdigest()[:8]
    return f"{function_name}_{hash_hex}"

def save_to_cache(function_name: str, result, *args, **kwargs):
    """保存结果到缓存"""
    cache_dir = ensure_cache_dir()
    cache_key = generate_cache_key(function_name, *args, **kwargs)
    
    cache_data = {
        'function_name': function_name,
        'cache_key': cache_key,
        'result': result,
        'timestamp': datetime.now().isoformat(),
        'mode': 'intranet'
    }
    
    cache_file = os.path.join(cache_dir, f"{cache_key}.json")
    
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
        logger.info(f"✓ 缓存已保存: {cache_file}")
        return True
    except Exception as e:
        logger.error(f"✗ 缓存保存失败: {e}")
        return False

# ==================== 邮件服务 ====================
def call_check_new_mail():
    """调用邮件检查函数"""
    logger.info("正在调用邮件检查函数...")
    
    try:
        # 这里需要复制email_server.py中check_new_mail函数的实际实现
        # 为了演示，我们先导入并调用
        from email_server import check_new_mail
        
        # 移除装饰器，直接调用函数逻辑
        all_unread_emails, total_count = check_new_mail()
        
        result = {
            "status": "success",
            "message": f"成功检查邮件，发现 {total_count} 封未读邮件",
            "data": {
                "total_emails": total_count,
                "emails": all_unread_emails
            }
        }
        
        save_to_cache('check_new_mail', result)
        logger.info(f"✓ 邮件检查完成，发现 {total_count} 封未读邮件")
        return result
        
    except Exception as e:
        logger.error(f"✗ 邮件检查失败: {e}")
        error_result = {
            "status": "error",
            "message": f"邮件检查失败: {str(e)}",
            "data": None
        }
        save_to_cache('check_new_mail', error_result)
        return error_result

# ==================== S6000业务流服务 ====================
def call_get_pending_items():
    """调用S6000业务流查询函数"""
    logger.info("正在调用S6000业务流查询函数...")
    
    try:
        from s6000_server import get_pending_items
        
        result_data = get_pending_items()
        
        result = {
            "status": "success",
            "message": "成功获取S6000待处理事项",
            "data": result_data
        }
        
        save_to_cache('get_pending_items', result)
        logger.info("✓ S6000业务流查询完成")
        return result
        
    except Exception as e:
        logger.error(f"✗ S6000业务流查询失败: {e}")
        error_result = {
            "status": "error",
            "message": f"S6000业务流查询失败: {str(e)}",
            "data": None
        }
        save_to_cache('get_pending_items', error_result)
        return error_result

# ==================== 漏洞扫描服务 ====================
def call_vulnscan_functions():
    """调用漏洞扫描相关函数"""
    logger.info("正在调用漏洞扫描查询函数...")
    results = []
    
    try:
        from vulnscan_server import get_all_scan_tasks, get_task_vulnerabilities
        
        # 调用扫描任务列表
        scan_tasks_data = get_all_scan_tasks()
        scan_tasks_result = {
            "status": "success",
            "message": "成功获取扫描任务列表",
            "data": scan_tasks_data
        }
        save_to_cache('get_all_scan_tasks', scan_tasks_result)
        results.append(scan_tasks_result)
        logger.info("✓ 扫描任务列表查询完成")
        
        # 调用任务漏洞信息（使用默认任务ID）
        task_vulns_data = get_task_vulnerabilities(task_id="default_task")
        task_vulns_result = {
            "status": "success",
            "message": "成功获取任务漏洞信息",
            "data": task_vulns_data
        }
        save_to_cache('get_task_vulnerabilities', task_vulns_result, "default_task")
        results.append(task_vulns_result)
        logger.info("✓ 任务漏洞信息查询完成")
        
    except Exception as e:
        logger.error(f"✗ 漏洞扫描查询失败: {e}")
        error_result = {
            "status": "error",
            "message": f"漏洞扫描查询失败: {str(e)}",
            "data": None
        }
        results.append(error_result)
    
    return results

# ==================== 防火墙服务 ====================
def call_firewall_functions():
    """调用防火墙相关函数"""
    logger.info("正在调用防火墙操作函数...")
    results = []
    test_ip = "*************"
    test_reason = "测试封禁"
    
    try:
        from firewall_server import block_ip_address, query_block_status
        
        # 调用IP封禁
        block_data = block_ip_address(ip_address=test_ip, reason=test_reason)
        block_result = {
            "status": "success",
            "message": "IP地址封禁操作完成",
            "data": block_data
        }
        save_to_cache('block_ip_address', block_result, test_ip, test_reason)
        results.append(block_result)
        logger.info(f"✓ IP封禁操作完成: {test_ip}")
        
        # 调用封禁状态查询
        query_data = query_block_status(ip_address=test_ip)
        query_result = {
            "status": "success",
            "message": "封禁状态查询完成",
            "data": query_data
        }
        save_to_cache('query_block_status', query_result, test_ip)
        results.append(query_result)
        logger.info(f"✓ 封禁状态查询完成: {test_ip}")
        
    except Exception as e:
        logger.error(f"✗ 防火墙操作失败: {e}")
        error_result = {
            "status": "error",
            "message": f"防火墙操作失败: {str(e)}",
            "data": None
        }
        results.append(error_result)
    
    return results

# ==================== 天眼告警服务 ====================
def call_tianyan_alerts():
    """调用天眼告警查询函数"""
    logger.info("正在调用天眼告警查询函数...")
    results = []
    
    # 不同参数组合
    test_params = [
        (5, 50),   # 最近5分钟，限制50条
        (10, 100), # 最近10分钟，限制100条
        (30, 200)  # 最近30分钟，限制200条
    ]
    
    try:
        from tianyan_alert_server import query_tianyan_alerts
        
        for minutes_ago, limit in test_params:
            alert_data = query_tianyan_alerts(minutes_ago=minutes_ago, limit=limit)
            alert_result = {
                "status": "success",
                "message": f"成功查询天眼告警（最近{minutes_ago}分钟，限制{limit}条）",
                "data": alert_data
            }
            save_to_cache('query_tianyan_alerts', alert_result, minutes_ago, limit)
            results.append(alert_result)
            logger.info(f"✓ 天眼告警查询完成: {minutes_ago}分钟, {limit}条")
            
    except Exception as e:
        logger.error(f"✗ 天眼告警查询失败: {e}")
        error_result = {
            "status": "error",
            "message": f"天眼告警查询失败: {str(e)}",
            "data": None
        }
        results.append(error_result)
    
    return results

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("直接调用MCP工具函数")
    logger.info("获取真实结果并保存到缓存")
    logger.info("=" * 60)
    
    # 确保缓存目录存在
    ensure_cache_dir()
    
    # 调用所有工具函数
    all_results = {}
    
    all_results['email'] = call_check_new_mail()
    all_results['s6000'] = call_get_pending_items()
    all_results['vulnscan'] = call_vulnscan_functions()
    all_results['firewall'] = call_firewall_functions()
    all_results['tianyan'] = call_tianyan_alerts()
    
    logger.info("=" * 60)
    logger.info("所有工具函数调用完成")
    logger.info("=" * 60)
    
    # 统计成功的调用
    success_count = 0
    total_count = 0
    
    for service, result in all_results.items():
        if isinstance(result, list):
            for r in result:
                total_count += 1
                if r.get('status') == 'success':
                    success_count += 1
        else:
            total_count += 1
            if result.get('status') == 'success':
                success_count += 1
    
    logger.info(f"成功调用: {success_count}/{total_count}")
    logger.info("\n🎉 缓存生成完成！")
    logger.info("现在可以将整个项目文件夹（包括cache_data目录）复制到外网使用")

if __name__ == "__main__":
    main()
