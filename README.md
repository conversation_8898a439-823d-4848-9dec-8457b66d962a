# MCP工具系统

这是一个支持内网/外网环境的MCP工具系统，通过直接调用真实函数生成缓存。

## 🚀 功能特点

- **直接调用**: 直接调用真实的MCP工具函数获取实际结果
- **缓存生成**: 在内网环境生成真实数据的缓存文件
- **外网使用**: 将缓存文件复制到外网直接使用
- **完整支持**: 支持所有MCP工具函数

## 📁 项目结构

```
outMcp/
├── main_server.py          # 主服务器，提供流式对话 API
├── direct_tool_caller.py   # 直接调用工具函数生成缓存
├── test_single_tool.py     # 单个函数测试工具
├── test_client.py          # 测试客户端
├── config.env              # 配置文件
├── cache_data/             # 缓存数据目录
└── tools/                  # 工具目录
    ├── email_server.py     # 邮件服务工具
    ├── s6000_server.py     # S6000业务流工具
    ├── vulnscan_server.py  # 漏洞扫描工具
    ├── firewall_server.py  # 防火墙服务工具
    └── tianyan_alert_server.py # 天眼告警工具
```

## 🎯 使用方法

### 1. 内网环境 - 生成缓存

#### 测试单个函数（推荐先测试）
```bash
# 测试邮件函数
python test_single_tool.py email

# 测试S6000函数
python test_single_tool.py s6000

# 测试所有函数
python test_single_tool.py all
```

#### 生成完整缓存
```bash
# 调用所有真实函数并生成缓存
python direct_tool_caller.py
```

### 2. 复制到外网

将整个项目文件夹（包括生成的`cache_data`目录）复制到外网环境。

### 3. 外网环境使用

在外网环境中直接启动服务：

```bash
python main_server.py
```

## 🛠️ 环境要求

- Python 3.8+
- Ollama (本地运行，端口 11444)
- 所需 Python 包：
  ```bash
  pip install fastapi uvicorn langchain-ollama langchain-mcp-adapters langgraph
  ```

## 📋 支持的工具

### 1. 邮件服务 (email_server.py)
- **功能**: 检查新邮件
- **工具方法**: `check_new_mail()`

### 2. S6000业务流服务 (s6000_server.py)
- **功能**: 获取业务流待处理事项
- **工具方法**: `get_pending_items(module_type)`
- **支持模块**: 工作通知、工作任务、工作联系单、预警单反馈、全省预警通告

### 3. 漏洞扫描服务 (vulnscan_server.py)
- **功能**: 查询扫描任务和漏洞信息
- **工具方法**: `get_all_scan_tasks()`, `get_task_vulnerabilities()`

### 4. 防火墙服务 (firewall_server.py)
- **功能**: IP封禁和状态查询
- **工具方法**: `block_ip_address()`, `query_block_status()`

### 5. 天眼告警服务 (tianyan_alert_server.py)
- **功能**: 查询安全告警信息
- **工具方法**: `query_tianyan_alerts()`

## 🔧 工作原理

### 直接调用机制

1. **导入函数**: 直接从各服务器文件导入MCP工具函数
2. **移除装饰器**: 绕过MCP装饰器，直接调用业务逻辑
3. **获取真实结果**: 调用实际的业务逻辑获取真实数据
4. **保存缓存**: 将结果保存为JSON文件供外网使用

### 缓存文件格式

```json
{
  "function_name": "check_new_mail",
  "cache_key": "check_new_mail_abc12345",
  "result": {
    "status": "success",
    "data": { ... }
  },
  "timestamp": "2025-08-02T10:30:00",
  "mode": "intranet"
}
```

## ✅ 优势

- **真实数据**: 获取实际的业务数据，不是模拟数据
- **完整功能**: 支持所有MCP工具函数
- **错误处理**: 即使某些函数失败也会保存错误信息到缓存
- **灵活测试**: 可以单独测试每个函数
- **自动缓存**: 生成的缓存文件可直接在外网使用

## ⚠️ 注意事项

1. **首次运行**: 必须在内网环境先运行生成缓存
2. **完整复制**: 复制到外网时必须包含`cache_data`目录
3. **服务依赖**: 内网环境需要相关服务正常运行
4. **定期更新**: 内网数据更新后需要重新生成缓存

## 🔧 故障排除

### 测试步骤

1. **单个函数测试**
   ```bash
   python test_single_tool.py email
   ```

2. **查看错误详情**
   - 测试工具会显示详细的错误信息和堆栈跟踪

3. **检查服务状态**
   - 确认相关服务（邮件服务器、数据库等）是否正常运行

4. **生成完整缓存**
   ```bash
   python direct_tool_caller.py
   ```

## 📚 相关资源

- [Model Context Protocol 官方文档](https://modelcontextprotocol.io/)
- [FastMCP 文档](https://github.com/jlowin/fastmcp)
- [LangChain MCP 适配器](https://github.com/langchain-ai/langchain-mcp-adapters)

